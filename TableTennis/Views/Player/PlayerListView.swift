import SwiftUI

struct PlayerListView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var showingAddPlayer = false
    @Binding var showingSettings: Bool

    @State private var sortOption: PlayerSortOption = .elo

    enum PlayerSortOption: String, CaseIterable {
        case elo = "ELO"
        case matches = "Matches"
        case matchWinPercentage = "Match %"
        case gameWinPercentage = "Game %"
    }

    var filteredAndSortedPlayers: [Player] {
        print("🎯 PlayerListView: Computing filteredAndSortedPlayers with \(dataManager.players.count) players")

        let sorted = dataManager.players.sorted { player1, player2 in
            switch sortOption {
            case .elo:
                return player1.eloRating > player2.eloRating
            case .matches:
                return player1.matchesPlayed > player2.matchesPlayed
            case .matchWinPercentage:
                return player1.matchWinPercentage > player2.matchWinPercentage
            case .gameWinPercentage:
                return player1.gameWinPercentage > player2.gameWinPercentage
            }
        }

        print("🎯 PlayerListView: Returning \(sorted.count) filtered and sorted players")
        return sorted
    }

    var body: some View {
        VStack {
            // Sort Controls
            VStack(spacing: 12) {
                Picker("Sort by", selection: $sortOption) {
                    ForEach(PlayerSortOption.allCases, id: \.self) { option in
                        Text(option.rawValue).tag(option)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.horizontal)

            // Players List
            if filteredAndSortedPlayers.isEmpty {
                let _ = print("🎯 PlayerListView: Showing empty state - dataManager.players.count = \(dataManager.players.count)")
                EmptyStateView(
                    title: "No players",
                    subtitle: "Add your first player to get started",
                    systemImage: "person.2"
                )
            } else {
                let _ = print("🎯 PlayerListView: Showing \(filteredAndSortedPlayers.count) players")
                VStack(spacing: 0) {
                    // Header (only show on iPad)
                    if UIDevice.current.userInterfaceIdiom != .phone {
                        PlayerListHeaderView()
                            .padding(.horizontal)
                            .padding(.bottom, 8)
                    }

                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(Array(filteredAndSortedPlayers.enumerated()), id: \.element.id) { index, player in
                                NavigationLink(destination: PlayerDetailView(player: player)) {
                                    PlayerRowView(
                                        player: player,
                                        position: index + 1,
                                        showConfetti: index == 0 && sortOption == .elo
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
        }
        .navigationTitle("Players")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddPlayer = true }) {
                    Image(systemName: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    showingSettings = true
                } label: {
                    Image(systemName: "gear")
                }
            }
        }
        .sheet(isPresented: $showingAddPlayer) {
            AddPlayerView()
        }
        .onAppear {
            print("🎯 PlayerListView: onAppear - dataManager.players.count = \(dataManager.players.count)")
        }
    }


}

struct PlayerRowView: View {
    let player: Player
    let position: Int
    let showConfetti: Bool

    init(player: Player, position: Int, showConfetti: Bool = false) {
        self.player = player
        self.position = position
        self.showConfetti = showConfetti
    }

    private var isPhone: Bool {
        UIDevice.current.userInterfaceIdiom == .phone
    }

    var body: some View {
        if isPhone {
            // Compact iPhone layout
            VStack(alignment: .leading, spacing: 8) {
                // Top row: Position, Name, and ELO
                HStack {
                    // Position with medal styling
                    Text("#\(position)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(positionTextColor)
                        .frame(minWidth: 25)

                    Text(player.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    Spacer()

                    // ELO prominently displayed
                    Text("\(Int(player.eloRating))")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }

                // Bottom row: Compact statistics
                HStack(spacing: 12) {
                    // Matches
                    HStack(spacing: 4) {
                        Image(systemName: "gamecontroller")
                            .font(.caption)
                            .foregroundColor(.blue)
                        Text("\(player.matchesWon)/\(player.matchesPlayed)")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    // Match win percentage
                    if player.matchesPlayed > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.caption)
                                .foregroundColor(.green)
                            Text("\(Int(player.matchWinPercentage))%")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(winPercentageColor(player.matchWinPercentage))
                        }
                    }

                    Spacer()

                    // Games (more compact)
                    Text("G: \(player.gamesWon)/\(player.gamesPlayed)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(backgroundColor)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(borderColor, lineWidth: position <= 3 ? 2 : 0)
            )
            .overlay(
                // Confetti effect for #1 player
                Group {
                    if showConfetti {
                        SimpleConfettiView()
                            .allowsHitTesting(false)
                            .clipped()
                    }
                }
            )
        } else {
            // Original iPad layout
            HStack {
                // Position with medal styling
                VStack(alignment: .center, spacing: 2) {
                    Text("#\(position)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(positionTextColor)
                        .fixedSize()
                }
                .frame(minWidth: 35)

                Text(player.name)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                HStack(spacing: 16) {
                    VStack(alignment: .center, spacing: 2) {
                        Text("\(player.matchesWon)/\(player.matchesPlayed)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .fixedSize()
                    }
                    .frame(minWidth: 45)

                    VStack(alignment: .center, spacing: 2) {
                        if player.matchesPlayed > 0 {
                            Text("\(Int(player.matchWinPercentage))%")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(winPercentageColor(player.matchWinPercentage))
                                .fixedSize()
                        } else {
                            Text("-")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                                .fixedSize()
                        }
                    }
                    .frame(minWidth: 35)

                    VStack(alignment: .center, spacing: 2) {
                        Text("\(player.gamesWon)/\(player.gamesPlayed)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .fixedSize()
                    }
                    .frame(minWidth: 45)

                    VStack(alignment: .center, spacing: 2) {
                        if player.gamesPlayed > 0 {
                            Text("\(Int(player.gameWinPercentage))%")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(winPercentageColor(player.gameWinPercentage))
                                .fixedSize()
                        } else {
                            Text("-")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                                .fixedSize()
                        }
                    }
                    .frame(minWidth: 35)

                    VStack(alignment: .center, spacing: 2) {
                        Text("\(Int(player.eloRating))")
                            .font(.caption)
                            .fontWeight(.medium)
                            .fixedSize()
                    }
                    .frame(minWidth: 35)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(backgroundColor)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(borderColor, lineWidth: position <= 3 ? 2 : 0)
            )
            .overlay(
                // Confetti effect for #1 player
                Group {
                    if showConfetti {
                        SimpleConfettiView()
                            .allowsHitTesting(false)
                            .clipped()
                    }
                }
            )
        }
    }

    private var backgroundColor: Color {
        switch position {
        case 1:
            // Gold background - adaptive for light/dark mode
            return Color.yellow.opacity(0.15)
        case 2:
            // Silver background - adaptive for light/dark mode
            return Color.gray.opacity(0.12)
        case 3:
            // Bronze background - adaptive for light/dark mode
            return Color.orange.opacity(0.12)
        default:
            // Regular background
            return Color(.systemGray6)
        }
    }

    private var borderColor: Color {
        switch position {
        case 1:
            // Gold border - adaptive
            return Color.yellow.opacity(0.6)
        case 2:
            // Silver border - adaptive
            return Color.gray.opacity(0.5)
        case 3:
            // Bronze border - adaptive
            return Color.orange.opacity(0.6)
        default:
            return Color.clear
        }
    }

    private var positionTextColor: Color {
        switch position {
        case 1:
            // Gold text - adaptive and more vibrant
            return Color.yellow.opacity(0.9)
        case 2:
            // Silver text - adaptive
            return Color.gray.opacity(0.8)
        case 3:
            // Bronze text - adaptive
            return Color.orange.opacity(0.9)
        default:
            return .primary
        }
    }
    
    private func winPercentageColor(_ percentage: Double) -> Color {
        switch percentage {
        case 70...:
            return .green
        case 50..<70:
            return .orange
        default:
            return .red
        }
    }
}



struct EmptyStateView: View {
    let title: String
    let subtitle: String
    let systemImage: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: systemImage)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct PlayerListHeaderView: View {
    var body: some View {
        HStack {
            // Position
            VStack(alignment: .center, spacing: 2) {
                Text("Pos")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            .frame(minWidth: 35)

            // Name
            HStack {
                Text("Name")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                Spacer()
            }

            Spacer()

            HStack(spacing: 16) {
                // Matches
                VStack(alignment: .center, spacing: 2) {
                    Text("Matches")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(minWidth: 45)

                // Match %
                VStack(alignment: .center, spacing: 2) {
                    Text("M %")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(minWidth: 35)

                // Games
                VStack(alignment: .center, spacing: 2) {
                    Text("Games")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(minWidth: 45)

                // Game %
                VStack(alignment: .center, spacing: 2) {
                    Text("G %")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(minWidth: 35)

                // ELO
                VStack(alignment: .center, spacing: 2) {
                    Text("ELO")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(minWidth: 35)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    PlayerListView(showingSettings: .constant(false))
        .environmentObject(DataManager())
}
